# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 86ms
    [gap of 10ms]
    create-variant-model 67ms
    create-ARMEABI_V7A-model 85ms
    create-ARM64_V8A-model 19ms
    create-X86-model 11ms
    create-X86_64-model 11ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 24ms
    create-ARM64_V8A-model 11ms
    create-X86-model 18ms
    create-X86_64-model 21ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
  create-initial-cxx-model completed in 455ms
  [gap of 72ms]
create_cxx_tasks completed in 556ms

