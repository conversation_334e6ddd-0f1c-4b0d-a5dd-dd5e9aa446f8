{"buildFiles": ["E:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\BurgerNow\\BurgerNow\\my_app\\build\\.cxx\\Debug\\3y4z5m3x\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\BurgerNow\\BurgerNow\\my_app\\build\\.cxx\\Debug\\3y4z5m3x\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}