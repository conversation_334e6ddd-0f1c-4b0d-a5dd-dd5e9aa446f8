{"logs": [{"outputFile": "com.bilalkhan.my_app-mergeDebugResources-33:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b2f5c3c30f9d9c891267a5a118b08ffd\\transformed\\jetified-credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "218", "startColumns": "4", "startOffsets": "13569", "endLines": "221", "endColumns": "12", "endOffsets": "13787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b0197055dff40dcc84d6fc813d8471d1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "157", "startColumns": "4", "startOffsets": "8158", "endColumns": "49", "endOffsets": "8203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\877ed2693a6cb3e6a6e0552c64e2df3d\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "163", "startColumns": "4", "startOffsets": "8576", "endColumns": "82", "endOffsets": "8654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2bb02d4900c83d0b5f947eddb45bbeaa\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "133,137,158,313,318", "startColumns": "4,4,4,4,4", "startOffsets": "6908,7077,8208,17610,17780", "endLines": "133,137,158,317,321", "endColumns": "56,64,63,24,24", "endOffsets": "6960,7137,8267,17775,17924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c98b28a74036481470d0f16288d3bf56\\transformed\\jetified-core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "222", "startColumns": "4", "startOffsets": "13792", "endLines": "229", "endColumns": "8", "endOffsets": "14197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\948f81ec80af2ef66520cdff033aecf5\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "154", "startColumns": "4", "startOffsets": "8001", "endColumns": "42", "endOffsets": "8039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9da873ba1fb32b12c8c947b0ed5d14cd\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "161,162", "startColumns": "4,4", "startOffsets": "8410,8492", "endColumns": "81,83", "endOffsets": "8487,8571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c6361265a592f93c73f703b30497e8ed\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "156", "startColumns": "4", "startOffsets": "8104", "endColumns": "53", "endOffsets": "8153"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0d14b031b15e488141b5722868803b3c\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,132,232,238,393,401,416", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,164,337,556,835,1149,1337,1524,1577,1637,1689,1734,6848,14341,14536,19510,19792,20406", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,132,237,242,400,415,431", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,332,551,770,1144,1332,1519,1572,1632,1684,1729,1768,6903,14531,14689,19787,20401,21055"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbb0ff8cbbcd0ae271b45ce93dcca2d4\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "159,179", "startColumns": "4,4", "startOffsets": "8272,10210", "endColumns": "67,166", "endOffsets": "8335,10372"}}, {"source": "E:\\BurgerNow\\BurgerNow\\my_app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "200,204", "startColumns": "4,4", "startOffsets": "12461,12642", "endLines": "203,206", "endColumns": "12,12", "endOffsets": "12637,12806"}}, {"source": "E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\generated\\res\\google-services\\debug\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,580", "endColumns": "81,103,108,119,109,78", "endOffsets": "132,236,345,465,575,654"}, "to": {"startLines": "193,194,195,196,197,198", "startColumns": "4,4,4,4,4,4", "startOffsets": "11786,11868,11972,12081,12201,12311", "endColumns": "81,103,108,119,109,78", "endOffsets": "11863,11967,12076,12196,12306,12385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ba8466007c560b2adf3f3486d3cfaf1e\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "136,155", "startColumns": "4,4", "startOffsets": "7035,8044", "endColumns": "41,59", "endOffsets": "7072,8099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43f536c6843ee4b82ffed3ee7d27eb4b\\transformed\\jetified-play-services-base-18.0.1\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "63,64,65,66,67,68,69,70,171,172,173,174,175,176,177,178,180,181,182,183,184,185,186,187,188,361,374", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2313,2403,2483,2573,2663,2743,2824,2904,9170,9275,9456,9581,9688,9868,9991,10107,10377,10565,10670,10851,10976,11151,11299,11362,11424,18778,19093", "endLines": "63,64,65,66,67,68,69,70,171,172,173,174,175,176,177,178,180,181,182,183,184,185,186,187,188,373,392", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2398,2478,2568,2658,2738,2819,2899,2979,9270,9451,9576,9683,9863,9986,10102,10205,10560,10665,10846,10971,11146,11294,11357,11419,11498,19088,19505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68a609b5de2b59c2ace95117d3eb10e8\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,61,62,71,72,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,134,135,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,160,164,165,166,167,168,169,170,199,207,208,212,213,217,230,231,243,249,259,292,322,355", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,1773,1845,2182,2247,2984,3053,3259,3329,3397,3469,3539,3600,3674,3747,3808,3869,3931,3995,4057,4118,4186,4286,4346,4412,4485,4554,4611,4663,4725,4797,4873,4938,4997,5056,5116,5176,5236,5296,5356,5416,5476,5536,5596,5656,5715,5775,5835,5895,5955,6015,6075,6135,6195,6255,6315,6374,6434,6494,6553,6612,6671,6730,6789,6965,7000,7142,7197,7260,7315,7373,7431,7492,7555,7612,7663,7713,7774,7831,7897,7931,7966,8340,8659,8726,8798,8867,8936,9010,9082,12390,12811,12928,13129,13239,13440,14202,14274,14694,14897,15198,16929,17929,18611", "endLines": "25,55,56,61,62,71,72,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,134,135,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,160,164,165,166,167,168,169,170,199,207,211,212,216,217,230,231,248,258,291,312,354,360", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "830,1840,1928,2242,2308,3048,3111,3324,3392,3464,3534,3595,3669,3742,3803,3864,3926,3990,4052,4113,4181,4281,4341,4407,4480,4549,4606,4658,4720,4792,4868,4933,4992,5051,5111,5171,5231,5291,5351,5411,5471,5531,5591,5651,5710,5770,5830,5890,5950,6010,6070,6130,6190,6250,6310,6369,6429,6489,6548,6607,6666,6725,6784,6843,6995,7030,7192,7255,7310,7368,7426,7487,7550,7607,7658,7708,7769,7826,7892,7926,7961,7996,8405,8721,8793,8862,8931,9005,9077,9165,12456,12923,13124,13234,13435,13564,14269,14336,14892,15193,16924,17605,18606,18773"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\72a213967fcdb1d583310dee0a0bf6f7\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "57,58,59,60,73,74,189,190,191,192", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "1933,1991,2057,2120,3116,3187,11503,11571,11638,11717", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "1986,2052,2115,2177,3182,3254,11566,11633,11712,11781"}}]}]}