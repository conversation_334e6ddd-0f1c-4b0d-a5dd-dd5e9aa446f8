{"logs": [{"outputFile": "com.bilalkhan.my_app-mergeDebugResources-33:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9da873ba1fb32b12c8c947b0ed5d14cd\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,108", "endOffsets": "156,265"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbb0ff8cbbcd0ae271b45ce93dcca2d4\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1795", "endColumns": "107", "endOffsets": "1898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\72a213967fcdb1d583310dee0a0bf6f7\\transformed\\browser-1.4.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2757,2840,2932,3033", "endColumns": "82,91,100,92", "endOffsets": "2835,2927,3028,3121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43f536c6843ee4b82ffed3ee7d27eb4b\\transformed\\jetified-play-services-base-18.0.1\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "931,1032,1160,1275,1377,1484,1600,1702,1903,2013,2114,2243,2358,2465,2573,2628,2685", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "1027,1155,1270,1372,1479,1595,1697,1790,2008,2109,2238,2353,2460,2568,2623,2680,2752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68a609b5de2b59c2ace95117d3eb10e8\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "4,5,6,7,8,9,10,33", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "270,362,461,555,649,742,835,3126", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "357,456,550,644,737,830,926,3222"}}]}]}