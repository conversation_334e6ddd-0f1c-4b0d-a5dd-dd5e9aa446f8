 E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\images\\Backgroundicons.png E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\images\\facebook_icon.png E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\images\\firstscreen.png E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\images\\google_icon.png E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\images\\Logo.png E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\images\\Organetopshape.png E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\images\\secondscreen.png E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\images\\Shapedsubtraction.png E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\images\\thirdscreen.png E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z E:\\BurgerNow\\BurgerNow\\my_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json:  E:\\BurgerNow\\BurgerNow\\my_app\\pubspec.yaml E:\\BurgerNow\\BurgerNow\\my_app\\assets\\images\\Backgroundicons.png E:\\BurgerNow\\BurgerNow\\my_app\\assets\\images\\facebook_icon.png E:\\BurgerNow\\BurgerNow\\my_app\\assets\\images\\firstscreen.png E:\\BurgerNow\\BurgerNow\\my_app\\assets\\images\\google_icon.png E:\\BurgerNow\\BurgerNow\\my_app\\assets\\images\\Logo.png E:\\BurgerNow\\BurgerNow\\my_app\\assets\\images\\Organetopshape.png E:\\BurgerNow\\BurgerNow\\my_app\\assets\\images\\secondscreen.png E:\\BurgerNow\\BurgerNow\\my_app\\assets\\images\\Shapedsubtraction.png E:\\BurgerNow\\BurgerNow\\my_app\\assets\\images\\thirdscreen.png C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf E:\\Flutter\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf E:\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag E:\\BurgerNow\\BurgerNow\\my_app\\.dart_tool\\flutter_build\\740c283f0a3390195da863084fa31e03\\native_assets.json C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\_flutterfire_internals-1.3.49\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cloud_firestore-5.6.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cloud_firestore_platform_interface-6.6.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cloud_firestore_web-4.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_auth-5.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_auth_platform_interface-7.5.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_auth_web-5.13.6\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core-3.10.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core_platform_interface-5.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core_web-2.19.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.0\\LICENSE E:\\Flutter\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE E:\\Flutter\\flutter\\packages\\flutter\\LICENSE E:\\BurgerNow\\BurgerNow\\my_app\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD811586222