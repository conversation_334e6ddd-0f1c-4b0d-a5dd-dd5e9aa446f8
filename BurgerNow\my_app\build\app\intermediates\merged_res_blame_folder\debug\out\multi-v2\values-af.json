{"logs": [{"outputFile": "com.bilalkhan.my_app-mergeDebugResources-33:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\72a213967fcdb1d583310dee0a0bf6f7\\transformed\\browser-1.4.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,262,375", "endColumns": "104,101,112,99", "endOffsets": "155,257,370,470"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "3217,3322,3424,3537", "endColumns": "104,101,112,99", "endOffsets": "3317,3419,3532,3632"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9da873ba1fb32b12c8c947b0ed5d14cd\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,121", "endOffsets": "162,284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbb0ff8cbbcd0ae271b45ce93dcca2d4\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-af\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2015", "endColumns": "142", "endOffsets": "2153"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43f536c6843ee4b82ffed3ee7d27eb4b\\transformed\\jetified-play-services-base-18.0.1\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,448,570,676,822,940,1057,1155,1317,1421,1574,1697,1832,1982,2044,2103", "endColumns": "102,151,121,105,145,117,116,97,161,103,152,122,134,149,61,58,74", "endOffsets": "295,447,569,675,821,939,1056,1154,1316,1420,1573,1696,1831,1981,2043,2102,2177"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1021,1128,1284,1410,1520,1670,1792,1913,2158,2324,2432,2589,2716,2855,3009,3075,3138", "endColumns": "106,155,125,109,149,121,120,101,165,107,156,126,138,153,65,62,78", "endOffsets": "1123,1279,1405,1515,1665,1787,1908,2010,2319,2427,2584,2711,2850,3004,3070,3133,3212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\68a609b5de2b59c2ace95117d3eb10e8\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "4,5,6,7,8,9,10,33", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "289,387,489,587,685,792,901,3637", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "382,484,582,680,787,896,1016,3733"}}]}]}